// shared/UI/modals/ChangePasswordModal.tsx
"use client";

import React, { useState } from 'react';
import BaseModal from './BaseModal';
import { SpkFormInput } from '@/shared/UI/components';
import { encodePassword } from '@/shared/utils/passwordEncryption';
import { useAuthStore } from '@/shared/stores/authStore';

interface ChangePasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  isLoading?: boolean;
}

interface ChangePasswordFormData {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}

/**
 * ChangePasswordModal Component
 * 
 * A modal component for changing user password using the tenant change password API.
 * Features:
 * - Old password validation
 * - New password confirmation
 * - Password encryption using encodePassword function
 * - Integration with /api/admin/tenants/change-password endpoint
 */
const ChangePasswordModal: React.FC<ChangePasswordModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  isLoading = false,
}) => {
  const [formData, setFormData] = useState<ChangePasswordFormData>({
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPasswords, setShowPasswords] = useState({
    old: false,
    new: false,
    confirm: false
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { token } = useAuthStore();

  // Handle input changes
  const handleInputChange = (field: keyof ChangePasswordFormData) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Toggle password visibility
  const togglePasswordVisibility = (field: 'old' | 'new' | 'confirm') => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.oldPassword.trim()) {
      newErrors.oldPassword = 'Current password is required';
    }

    if (!formData.newPassword.trim()) {
      newErrors.newPassword = 'New password is required';
    } else if (formData.newPassword.length < 6) {
      newErrors.newPassword = 'New password must be at least 6 characters';
    }

    if (!formData.confirmPassword.trim()) {
      newErrors.confirmPassword = 'Please confirm your new password';
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (!token) {
      setErrors({ general: 'Authentication token not found. Please log in again.' });
      return;
    }

    setIsSubmitting(true);

    try {
      // Encrypt passwords using the same method as the API expects
      const encryptedOldPassword = encodePassword(formData.oldPassword);
      const encryptedNewPassword = encodePassword(formData.newPassword);

      const response = await fetch('https://adminapi.ingrandstation.com/api/admin/tenants/change-password', {
        method: 'POST',
        headers: {
          'accept': 'application/json, text/plain, */*',
          'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8',
          'authorization': `Bearer ${token}`,
          'content-type': 'application/json',
          'origin': 'https://iadmin.ingrandstation.com',
          'referer': 'https://iadmin.ingrandstation.com/',
        },
        body: JSON.stringify({
          old_password: encryptedOldPassword, // eslint-disable-line camelcase
          confirm_password: encryptedNewPassword // eslint-disable-line camelcase
        }),
      });

      if (response.ok) {
        await response.json();

        // Reset form
        setFormData({
          oldPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
        setErrors({});

        // Call success callback
        onSuccess?.();

        // Close modal
        onClose();
      } else {
        const errorData = await response.json();
        setErrors({
          general: errorData.message || 'Failed to change password. Please try again.'
        });
      }
    } catch {
      setErrors({
        general: 'Network error. Please check your connection and try again.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Reset form when modal closes
  const handleClose = () => {
    setFormData({
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    setErrors({});
    setShowPasswords({
      old: false,
      new: false,
      confirm: false
    });
    onClose();
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={handleClose}
      title="Change Password"
      headerIcon={<i className="ri-lock-password-line text-golden text-lg"></i>}
      size="sm"
      showBackdrop={true}
      bodyClassName="p-0"
    >
      <div style={{ padding: '12px 16px', gap: '16px' }}>
        <form onSubmit={handleSubmit} className="flex flex-col h-full" style={{ gap: '16px' }}>
          {/* General Error */}
          {errors.general && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
              <p className="text-red-400 text-sm">{errors.general}</p>
            </div>
          )}

          {/* Form Fields */}
          <div className="flex flex-col" style={{ gap: '16px' }}>
            {/* Current Password Field */}
            <div className="flex flex-col gap-[8px]">
              <label
                className="font-rubik font-semibold text-white capitalize"
                style={{ fontSize: '14px', fontWeight: 600 }}
              >
                Current Password <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <SpkFormInput
                  type={showPasswords.old ? "text" : "password"}
                  placeholder="Enter current password"
                  value={formData.oldPassword}
                  onChange={handleInputChange('oldPassword')}
                  className={`!mb-0 pr-10 ${errors.oldPassword ? 'border-red-500' : ''}`}
                  disabled={isSubmitting || isLoading}
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('old')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300 transition-colors"
                >
                  <i className={`${showPasswords.old ? 'ri-eye-off-line' : 'ri-eye-line'} text-sm`}></i>
                </button>
              </div>
              {errors.oldPassword && (
                <p className="text-red-500 text-sm">{errors.oldPassword}</p>
              )}
            </div>

            {/* New Password Field */}
            <div className="flex flex-col gap-[8px]">
              <label
                className="font-rubik font-semibold text-white capitalize"
                style={{ fontSize: '14px', fontWeight: 600 }}
              >
                New Password <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <SpkFormInput
                  type={showPasswords.new ? "text" : "password"}
                  placeholder="Enter new password"
                  value={formData.newPassword}
                  onChange={handleInputChange('newPassword')}
                  className={`!mb-0 pr-10 ${errors.newPassword ? 'border-red-500' : ''}`}
                  disabled={isSubmitting || isLoading}
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('new')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300 transition-colors"
                >
                  <i className={`${showPasswords.new ? 'ri-eye-off-line' : 'ri-eye-line'} text-sm`}></i>
                </button>
              </div>
              {errors.newPassword && (
                <p className="text-red-500 text-sm">{errors.newPassword}</p>
              )}
            </div>

            {/* Confirm Password Field */}
            <div className="flex flex-col gap-[8px]">
              <label
                className="font-rubik font-semibold text-white capitalize"
                style={{ fontSize: '14px', fontWeight: 600 }}
              >
                Confirm New Password <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <SpkFormInput
                  type={showPasswords.confirm ? "text" : "password"}
                  placeholder="Confirm new password"
                  value={formData.confirmPassword}
                  onChange={handleInputChange('confirmPassword')}
                  className={`!mb-0 pr-10 ${errors.confirmPassword ? 'border-red-500' : ''}`}
                  disabled={isSubmitting || isLoading}
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('confirm')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300 transition-colors"
                >
                  <i className={`${showPasswords.confirm ? 'ri-eye-off-line' : 'ri-eye-line'} text-sm`}></i>
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="text-red-500 text-sm">{errors.confirmPassword}</p>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            {/* Cancel Button */}
            <button
              type="button"
              className="font-rubik flex-1 font-bold bg-gray-600 p-[16px] rounded-[8px] text-[18px] text-white transition-colors duration-200 hover:bg-gray-500 disabled:opacity-50"
              disabled={isSubmitting || isLoading}
              onClick={handleClose}
            >
              Cancel
            </button>

            {/* Submit Button */}
            <button
              type="submit"
              className="font-rubik flex-1 font-bold bg-secondary p-[16px] rounded-[8px] text-[18px] text-white transition-colors duration-200 hover:bg-opacity-80 disabled:opacity-50"
              disabled={isSubmitting || isLoading}
            >
              {isSubmitting || isLoading ? 'Changing...' : 'Change Password'}
            </button>
          </div>
        </form>
      </div>
    </BaseModal>
  );
};

export default ChangePasswordModal;
