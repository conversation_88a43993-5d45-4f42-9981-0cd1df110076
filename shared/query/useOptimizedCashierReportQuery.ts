// shared/query/useOptimizedCashierReportQuery.ts
import { useQuery, keepPreviousData } from '@tanstack/react-query';
import { useAuthStore } from '@/shared/stores/authStore';
import {
  CashierReportFilters,
  CashierReportResponse,
  DEFAULT_CASHIER_REPORT_FILTERS
} from '@/shared/types/report-types';
import { checkAndHandle401 } from '@/shared/utils/globalApiErrorHandler';
import { transformActionTypeForApi } from '@/shared/config/transactionTypes';
import { generateTimePeriod } from './utils';

// Import the shared mapping from financial report query (not used directly, but kept for reference)
// import { CODE_TO_TRANSACTION_TYPES } from './useFinancialReportQuery';

/**
 * Get action type from transaction type code
 */
const getActionTypeFromTransactionType = (transactionType: string | number): string => {
  const typeMap: { [key: string]: string } = {
    '0': 'DEBIT',
    '1': 'CREDIT',
    '2': 'ROLLBACK',
    '3': 'DEPOSIT',
    '4': 'WITHDRAW',
    '14': 'WITHDRAW_CANCEL',
    '21': 'bet',
    '22': 'win',
  };
  return typeMap[transactionType.toString()] || transactionType.toString();
};

/**
 * Extract market information from meta_data array
 */
const extractMarketInfo = (metaData: any[]): { marketId?: string; marketName?: string } => {
  if (!metaData || !Array.isArray(metaData) || metaData.length === 0) {
    return {};
  }

  const firstMarket = metaData[0];
  return {
    marketId: firstMarket.marketId,
    marketName: firstMarket.market
  };
};

/**
 * Generate description from meta_data array for bet transactions
 */
const generateDescriptionFromMetaData = (metaData: any[]): string => {
  if (!metaData || !Array.isArray(metaData) || metaData.length === 0) {
    return '';
  }

  // For bet transactions, create a description from the events
  const events = metaData.map(item => `${item.event} - ${item.market}: ${item.outcome}`);
  return events.join('; ');
};

/**
 * Build query string for optimized cashier report API with rowsCountTotal support
 */
const buildOptimizedCashierReportQuery = (
  filters: CashierReportFilters,
  options: {
    rowsCountTotal?: boolean;
  } = {}
): string => {
  const queryParts: string[] = [];
  let timePeriod: string = generateTimePeriod();

  // Required parameters
  queryParts.push(`size=${filters.size}`);
  queryParts.push(`page=${filters.page}`);

  // Required timePeriod parameter with exact format
  if (filters.dateRange && filters.dateRange.startDate && filters.dateRange.endDate) {
    timePeriod = generateTimePeriod(filters.dateRange);
  }
  queryParts.push(`timePeriod=${timePeriod}`);

  // Default parameters
  queryParts.push(`order=${filters.order || 'desc'}`);
  queryParts.push(`sortBy=${filters.sortBy || 'created_at'}`);
  queryParts.push(`timeZone=${encodeURIComponent(filters.timeZone || 'UTC +00:00')}`);
  queryParts.push(`timeZoneName=${encodeURIComponent(filters.timeZoneName || 'UTC +00:00')}`);

  // Optional filters
  if (filters.search) {
    queryParts.push(`search=${encodeURIComponent(filters.search)}`);
  }

  if (filters.transactionId) {
    queryParts.push(`transactionId=${encodeURIComponent(filters.transactionId)}`);
  }

  if (filters.debitTransactionId) {
    queryParts.push(`debitTransactionId=${encodeURIComponent(filters.debitTransactionId)}`);
  }

  if (filters.roundId) {
    queryParts.push(`roundId=${encodeURIComponent(filters.roundId)}`);
  }

  if (filters.tenantId) {
    queryParts.push(`tenantId=${encodeURIComponent(filters.tenantId)}`);
  }

  if (filters.actionType) {
    // Transform actionType from numeric value to title string for API
    const actionTypeTitle = transformActionTypeForApi(filters.actionType);
    if (actionTypeTitle) {
      // Don't encode the array brackets and quotes - API expects raw format like ["withdraw"]
      queryParts.push(`actionType=${actionTypeTitle}`);
    }
  }

  if (filters.userId) {
    queryParts.push(`userId=${encodeURIComponent(filters.userId)}`);
  }

  if (filters.agentId) {
    queryParts.push(`agentId=${encodeURIComponent(filters.agentId)}`);
  }

  // Add rowsCountTotal parameter if requested (single API call optimization)
  if (options.rowsCountTotal) {
    queryParts.push('rowsCountTotal=true');
  }

  return queryParts.join('&');
};

/**
 * Fetch optimized cashier report with rowsCountTotal support
 */
export const fetchOptimizedCashierReport = async (
  filters: CashierReportFilters,
  options: {
    rowsCountTotal?: boolean;
  } = {}
): Promise<CashierReportResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  // Use the staging backend URL from environment variables
  const baseUrl = process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL || process.env.NEXT_PUBLIC_ADMIN_BACKEND_URL;

  if (!baseUrl) {
    throw new Error('API base URL is not configured');
  }

  // Build query string
  const queryString = buildOptimizedCashierReportQuery(filters, options);

  const response = await fetch(`${baseUrl}/api/v2/admin/transactions?${queryString}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
  });

  // Check for 401 errors and handle them globally
  await checkAndHandle401(response);

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || `Failed to fetch cashier report: ${response.status}`);
  }

  const apiResponse = await response.json();

  // Transform to standardized format
  const result = apiResponse.data?.data?.result;
  const rawRows = result?.rows || [];

  // Transform each row to match CashierTransaction interface
  const transformedData = rawRows.map((row: any) => {
    const marketInfo = extractMarketInfo(row.meta_data);

    return {
      id: row.internal_tracking_id,
      transactionId: row.transaction_id,
      userName: row.from_wallet_uname || row.action_by_uname || 'Unknown',
      actionType: getActionTypeFromTransactionType(row.transaction_type),
      marketId: marketInfo.marketId,
      marketName: marketInfo.marketName,
      amount: parseFloat(row.amount) || 0,
      currency: row.currency || row.source_currency_id,
      balance: parseFloat(row.ending_balance) || 0,
      status: row.status || 'unknown',
      createdAt: row.created_at,
      description: generateDescriptionFromMetaData(row.meta_data) || row.comments || 'No description',
      gameProvider: row.game_provider,
      gameType: row.game_name
    };
  });

  return {
    data: transformedData,
    success: apiResponse.success,
    message: apiResponse.data?.message || '',
    errors: apiResponse.errors || [],
    count: result?.count,
    totalPages: result?.total_pages,
    currentPage: result?.current_page,
    // Add totals from the API response when rowsCountTotal=true
    totals: result?.totals || undefined
  };
};

/**
 * Hook for single optimized cashier report API call with rowsCountTotal=true
 * This replaces multiple API calls with a single call that includes data, count, and totals
 */
export const useOptimizedSingleCashierReport = (filters: Partial<CashierReportFilters>, enabled: boolean = true) => {
  const { token } = useAuthStore();

  const finalFilters: CashierReportFilters = {
    ...DEFAULT_CASHIER_REPORT_FILTERS,
    ...filters,
  };

  return useQuery<CashierReportResponse>({
    queryKey: ['cashierReportSingle', finalFilters],
    queryFn: async () => {
      return await fetchOptimizedCashierReport(finalFilters, {
        rowsCountTotal: true
      });
    },
    enabled: enabled && !!token,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    placeholderData: keepPreviousData,
  });
};
